import React from "react"

import type { ToolbarColorProps } from "../Toolbar/type/types"
import { ColorPicker } from "./ColorPicker"

export const ToolbarColor: React.FC<ToolbarColorProps> = ({
  label,
  value,
  onChange,
  disabled
}) => (
  <div
    className={`toolbar-color-picker ${disabled ? "disabled" : ""}`}
    title={label}
    onClick={(e) => e.stopPropagation()}
  >
    <ColorPicker value={value} onChange={onChange} disabled={disabled} />
  </div>
)
